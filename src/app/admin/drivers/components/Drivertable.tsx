'use client';
import React, { useEffect, useState, useMemo, useCallback } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
  Field,
  Label,
  Radio,
  RadioGroup,
} from '@headlessui/react';
import Tippy from '@tippyjs/react';
import { hideAll } from 'tippy.js';
import 'tippy.js/dist/tippy.css';
import { PiClockCounterClockwiseLight } from 'react-icons/pi';
import { ApproveDocument } from './ApproveDocument';
import { useRouter } from 'next/navigation';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  addTableDriverDetails,
  deleteDriver,
  disableDriverQuery,
  downloadReport,
  getTableDriverDetails,
} from '../state/queries';
import {
  StarIcon,
  AddDriver,
  Upload,
  Cloud,
  UserTick,
  UploadActive,
  ChevronDownIcon,
  DownloadIcon,
} from '@/icons';
import { queryClient } from '@/hooks/useGlobalContext';
import { toast } from 'react-toastify';
import DeleteModal from '@/components/ui/modal/deletemodal';
import FilterComponent from './Filter';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { GrDocumentDownload } from 'react-icons/gr';
import { IoMdClose } from 'react-icons/io';
import { IoIosSearch } from 'react-icons/io';
import { FaPlus } from 'react-icons/fa6';
import { subYears } from 'date-fns';
import Input from '@/components/form/input/InputField';
import Select from '@/components/form/Select';
import { DriverResponseType } from '@/utils/types';

// Constants moved outside component for better performance
const MODAL_YEAR_OPTIONS = Array.from({ length: 26 }, (_, i) => ({
  value: `${2000 + i}`,
  label: `${2000 + i}`,
})) as const;

const VEHICLE_OPTIONS = [
  { value: 'sedan', label: 'Sedan' },
  { value: 'van', label: 'Van' },
  { value: 'mini-van', label: 'Mini Van' },
  { value: 'suv', label: 'SUV' },
] as const;

const GENDER_OPTIONS = [
  { value: 'male', label: 'Male' },
  { value: 'other', label: 'Other' },
  { value: 'female', label: 'Female' },
] as const;

const VEHICLE_AVAILABILITY_OPTIONS = [
  { value: 'yes', label: 'Yes' },
  { value: 'no', label: 'No' },
] as const;

const STEPS = [
  {
    icon: AddDriver,
    active: AddDriver,
    label: 'Driver Details',
    subtitle: '10 Questions',
  },
  {
    icon: Upload,
    active: UploadActive,
    label: 'Upload Document',
    subtitle: '4 Documents',
  },
] as const;

const INITIAL_FORM_DATA = {
  fullName: '',
  email: '',
  gender: '',
  dob: null,
  nationalIdSsn: '',
  vehicleAvailability: '' as '' | 'yes' | 'no',
  model: '',
  plateNumber: '',
  vehicleType: '',
  address: '',
  countryCode: '+1',
  contactNo: '9876543210',
  shift: 'morning',
  region: 'North',
  rating: '4.5',
  city: 'demo',
  state: 'demo',
  zipCode: 'demo',
  color: 'Red',
  lastActive: '02-02-2025',
  driverStatus: 'Active',
  insuranceExpiryDate: null,
  insuranceRenewalReminder: 'true',
  vehicleRegistration: 'true',
  vehicleDetails: 'vehicleDetails',
  image: '',
} as const;

const INITIAL_FILTERS = {
  name: '',
  shift: '',
  ratings: '',
  region: '',
  rides: '',
} as const;

// Types
type Filters = {
  name: string;
  shift: string;
  ratings: string;
  region: string;
  rides: string;
};

type SortConfig = {
  key: string;
  direction: 'asc' | 'desc';
};

type DriverDetails = typeof INITIAL_FORM_DATA;

// Utility functions
const getDriverStatusClass = (status: string) => {
  const statusClasses = {
    Active: 'text-[#13BB76]',
    Inactive: 'text-[#8F8CD6]',
    Suspended: 'text-[#FF4032]',
    Enroute: 'text-[#1E90FF]',
  };
  return (
    statusClasses[status as keyof typeof statusClasses] || 'text-[#FF8C00]'
  );
};

const downloadFile = async (res: any) => {
  const url = window.URL.createObjectURL(new Blob([res], { type: 'text/csv' }));
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', 'data.csv');
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

// Custom Hooks
const useDriverForm = () => {
  const [formData, setFormData] = useState<DriverDetails>(INITIAL_FORM_DATA);
  const [driverLicenceFile, setDriverLicenceFile] = useState<File | null>(null);
  const [insuranceFile, setInsuranceFile] = useState<File | null>(null);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  }, []);

  const handleDriverDob = useCallback((date: Date | null) => {
    setFormData(prev => ({ ...prev, dob: date }));
  }, []);

  const handleDriverInsurance = useCallback((date: Date | null) => {
    setFormData(prev => ({
      ...prev,
      insuranceExpiryDate: date ? date.toISOString().split('T')[0] : '',
    }));
  }, []);

  const handleSelectChange = useCallback((name: string, value: string) => {
    if (name === 'vehicleAvailability' && value === 'no') {
      setFormData(prev => ({
        ...prev,
        [name]: value,
        model: '',
        plateNumber: '',
        vehicleType: '',
        insuranceExpiryDate: null,
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  }, []);

  const resetForm = useCallback(() => {
    setFormData(INITIAL_FORM_DATA);
    setDriverLicenceFile(null);
    setInsuranceFile(null);
    setErrors({});
  }, []);

  return {
    formData,
    driverLicenceFile,
    insuranceFile,
    errors,
    setErrors,
    handleChange,
    handleDriverDob,
    handleDriverInsurance,
    handleSelectChange,
    setDriverLicenceFile,
    setInsuranceFile,
    resetForm,
  };
};

const useDriverValidation = () => {
  const validate = useCallback((formData: DriverDetails) => {
    const newErrors: Record<string, string> = {};
    const platePattern = /^[A-Z]-\d{3}-[A-Z]{2}$/;

    const neverRequired = [
      'city',
      'state',
      'zipCode',
      'color',
      'lastActive',
      'driverStatus',
      'image',
      'countryCode',
      'contactNo',
      'shift',
      'region',
      'rating',
      'insuranceRenewalReminder',
      'vehicleRegistration',
      'vehicleDetails',
    ];

    const skipIfNoVehicle = [
      'model',
      'plateNumber',
      'vehicleType',
      'insuranceExpiryDate',
    ];
    const shouldSkipVehicleFields = formData.vehicleAvailability === 'no';

    for (const key in formData) {
      if (neverRequired.includes(key)) continue;
      if (shouldSkipVehicleFields && skipIfNoVehicle.includes(key)) continue;

      if (!formData[key as keyof DriverDetails]) {
        newErrors[key] = 'This field is required';
      }

      if (
        key === 'plateNumber' &&
        formData[key] &&
        !platePattern.test(formData[key].toString())
      ) {
        newErrors[key] = 'Please enter a valid code in the format: A-001-BB';
      }
    }

    return { errors: newErrors, isValid: Object.keys(newErrors).length === 0 };
  }, []);

  const validateFiles = useCallback(
    (driverLicenceFile: File | null, insuranceFile: File | null) => {
      const newErrors: Record<string, string> = {};

      if (!driverLicenceFile) {
        newErrors.driverLicenceFile = 'Driver Licence file is required';
      }

      if (!insuranceFile) {
        newErrors.insuranceFile = 'Insurance Copy file is required';
      }

      return {
        errors: newErrors,
        isValid: Object.keys(newErrors).length === 0,
      };
    },
    []
  );

  return { validate, validateFiles };
};

// eslint-disable-next-line react/display-name
const LoadingSpinner = React.memo(() => (
  <svg
    className="mr-2 -ml-1 h-4 w-4 animate-spin text-white"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    />
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    />
  </svg>
));

// eslint-disable-next-line react/display-name
const TableHeaderComponent = React.memo(
  ({
    sortConfig,
    requestSort,
    getSortIcon,
    selectedIds,
    sortedData,
    handleSelectAll,
  }: {
    sortConfig: SortConfig | null;
    requestSort: (key: string) => void;
    getSortIcon: (key: string) => string;
    selectedIds: number[];
    sortedData: any[];
    handleSelectAll: (e: React.ChangeEvent<HTMLInputElement>) => void;
  }) => {
    const columns = [
      { key: 'id', label: 'ID' },
      { key: 'fullName', label: 'Name' },
      { key: 'contactNo', label: 'Contact No' },
      { key: 'driverStatus', label: 'Status' },
      { key: 'shift', label: 'Shift' },
      { key: 'rating', label: 'Ratings' },
      { key: 'region', label: 'Region' },
      { key: 'plateNumber', label: 'Vehicle Details' },
      { key: 'rides', label: 'Rides' },
      { key: 'earnings', label: 'Earnings' },
      { key: 'registrationstatus', label: 'Verification' },
    ];

    return (
      <TableHeader className="border-b border-gray-100 bg-white text-[#76787A] dark:border-white/[0.05]">
        <TableRow>
          <TableCell
            isHeader
            className="text-theme-xs px-4 py-3 text-start text-[12px] font-medium text-[#76787A] dark:text-gray-400"
          >
            <label className="checkbox">
              <input
                type="checkbox"
                placeholder="Select Order"
                name="selectOrder"
                checked={
                  selectedIds.length === sortedData.length &&
                  sortedData.length > 0
                }
                onChange={handleSelectAll}
                className="form-checkbox text-blue-500"
              />
              <span></span>
            </label>
          </TableCell>
          {columns.map(column => (
            <TableCell
              key={column.key}
              isHeader
              className="text-76787A text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap dark:text-gray-400"
            >
              <button
                onClick={() => requestSort(column.key)}
                className="flex items-center"
              >
                {column.label}
                <span className="px-1 text-[11px]">
                  {getSortIcon(column.key)}
                </span>
              </button>
            </TableCell>
          ))}
          <TableCell
            isHeader
            className="text-76787A text-theme-xs py-3 text-right text-[12px] font-medium dark:text-gray-400"
          >
            &nbsp;
          </TableCell>
        </TableRow>
      </TableHeader>
    );
  }
);

// eslint-disable-next-line react/display-name
const ActionMenu = React.memo(
  ({
    order,
    router,
    handleDisableDriver,
    setOpenApproveDocument,
    downLoadDriverMutation,
    setSelectedId,
    setDeleteProfile,
  }: any) => (
    <Tippy
      trigger="click"
      content={
        <div className="bg-white text-gray-900">
          <div className="flex flex-col space-y-1 p-1">
            <button
              className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              onClick={() =>
                router.push(`/admin/drivers/${order?.id}?edit=true`)
              }
            >
              Edit Profile
            </button>
            <button
              onClick={() => handleDisableDriver(order?.id, 'Suspended')}
              className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
            >
              Suspend Driver
            </button>
            <button
              onClick={() => handleDisableDriver(order?.id, 'Active')}
              className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
            >
              Reinstate Driver
            </button>
            <button
              onClick={() => handleDisableDriver(order?.id, 'Inactive')}
              className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
            >
              Deactivate Driver
            </button>
            <button
              className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              onClick={() => setOpenApproveDocument(true)}
            >
              Approve Document
            </button>
            <button
              className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              onClick={() =>
                toast.success('Notification sent!', {
                  autoClose: 5000,
                  position: 'top-center',
                })
              }
            >
              Send Notification
            </button>
            <button
              onClick={() => downLoadDriverMutation.mutate(order?.id)}
              className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
            >
              Download Report
            </button>
            <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
              Trip Management
            </button>
            <button
              onClick={() => {
                setSelectedId(order.id);
                setDeleteProfile(true);
                hideAll();
              }}
              className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
            >
              Delete Profile
            </button>
          </div>
        </div>
      }
      interactive={true}
      placement="right"
      theme="light"
      arrow={false}
      duration={0}
      className="rounded-lg border border-gray-200 !bg-white !text-gray-900 shadow-sm"
    >
      <button
        type="button"
        className="text-[#76787A] focus:outline-none"
        aria-label="actions"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          className="size-6"
        >
          <path
            fillRule="evenodd"
            d="M10.5 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm0 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm0 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z"
            clipRule="evenodd"
          />
        </svg>
      </button>
    </Tippy>
  )
);

// eslint-disable-next-line react/display-name
const FormStep1 = React.memo(
  ({
    driverType,
    setDriverType,
    formData,
    errors,
    handleChange,
    handleSelectChange,
    handleDriverDob,
    handleDriverInsurance,
  }: any) => (
    <div className="mt-5 sm:mt-8">
      <div className="flex items-center justify-center rounded-xl border-gray-200">
        <div className="w-full">
          <p className="mb-3 text-sm font-medium text-[#050013] dark:text-white">
            Profile Overview
          </p>

          <RadioGroup
            value={driverType}
            onChange={setDriverType}
            aria-label="Driver type"
            className="mb-3"
          >
            <Field className="flex items-center gap-4 py-2">
              <Radio
                value="company-driver"
                id="company-driver"
                className={`group flex size-5 items-center justify-center rounded-full border bg-white data-checked:bg-blue-400 ${
                  driverType === 'company-driver'
                    ? 'cstm-radio'
                    : 'radio-border'
                }`}
              >
                <span className="invisible size-2 rounded-full bg-white group-data-checked:visible" />
              </Radio>
              <Label
                htmlFor="company-driver"
                className={`text-[13px] ${
                  driverType === 'company-driver'
                    ? 'font-medium text-[#050013]'
                    : 'font-normal text-[#76787A]'
                }`}
              >
                Company Driver
              </Label>
              <Radio
                value="contract-driver"
                id="contract-driver"
                className={`group flex size-5 items-center justify-center rounded-full border bg-white data-checked:bg-blue-400 ${
                  driverType === 'contract-driver'
                    ? 'cstm-radio'
                    : 'radio-border'
                }`}
              >
                <span className="invisible size-2 rounded-full bg-white group-data-checked:visible" />
              </Radio>
              <Label
                htmlFor="contract-driver"
                className={`text-[13px] ${
                  driverType === 'contract-driver'
                    ? 'font-medium text-[#050013]'
                    : 'font-normal text-[#76787A]'
                }`}
              >
                Contract Driver
              </Label>
            </Field>
          </RadioGroup>

          <div className="pb-2">
            <Input
              type="text"
              placeholder="Full Name*"
              onChange={handleChange}
              name="fullName"
              value={formData.fullName}
            />
            {errors?.fullName && (
              <p className="text-sm text-red-500">{errors?.fullName}</p>
            )}
          </div>

          <div className="py-2">
            <Input
              type="email"
              placeholder="Email ID*"
              onChange={e => {
                const email = e.target.value;
                const isValidEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
                handleChange(e);
                if (!isValidEmail && email) {
                  errors.email = 'Invalid email address';
                } else {
                  delete errors.email;
                }
              }}
              name="email"
              value={formData.email}
            />
            {errors?.email && (
              <p className="text-sm text-red-500">{errors?.email}</p>
            )}
          </div>

          <div className="flex w-full gap-4 py-2">
            <div className="relative w-full">
              <Select
                options={GENDER_OPTIONS}
                placeholder="Gender*"
                onChange={e => handleSelectChange('gender', e)}
                className="dark:bg-dark-900"
              />
              <span className="pointer-events-none absolute top-[22px] right-3 -translate-y-1/2 text-[#050013] dark:text-gray-400">
                <ChevronDownIcon />
              </span>
              {errors?.gender && (
                <p className="text-sm text-red-500">{errors?.gender}</p>
              )}
            </div>

            <div className="relative w-full">
              <DatePicker
                selected={formData?.dob}
                className="w-full rounded-lg border border-gray-300 p-2 text-gray-800"
                onChange={handleDriverDob}
                showYearDropdown
                showMonthDropdown
                scrollableYearDropdown
                dateFormat="dd/MM/yyyy"
                placeholderText="DOB"
                yearDropdownItemNumber={100}
                maxDate={subYears(new Date(), 18)}
              />
              <span className="pointer-events-none absolute top-[22px] right-3 -translate-y-1/2 text-[#050013] dark:text-gray-400">
                <ChevronDownIcon />
              </span>
              {errors?.dob && (
                <p className="text-sm text-red-500">{errors?.dob}</p>
              )}
            </div>
          </div>

          <Input
            type="text"
            placeholder="National ID/SSN"
            className="mt-3"
            onChange={handleChange}
            name="nationalIdSsn"
            value={formData.nationalIdSsn}
          />
          {errors?.nationalIdSsn && (
            <p className="text-sm text-red-500">{errors?.nationalIdSsn}</p>
          )}

          <p className="mb-3 pt-5 text-sm font-medium text-[#050013] dark:text-white">
            Vehicle Details
          </p>

          <div className="mt-3 mb-3">
            <div className="relative">
              <Select
                options={VEHICLE_AVAILABILITY_OPTIONS}
                placeholder="Vehicle Availability"
                onChange={e => handleSelectChange('vehicleAvailability', e)}
                className="dark:bg-dark-900"
              />
              <span className="pointer-events-none absolute top-[22px] right-3 -translate-y-1/2 text-[#050013] dark:text-gray-400">
                <ChevronDownIcon />
              </span>
            </div>
            {errors?.vehicleAvailability && (
              <p className="text-sm text-red-500">
                {errors?.vehicleAvailability}
              </p>
            )}
          </div>

          {formData.vehicleAvailability === 'yes' && (
            <>
              <div className="mb-3 grid grid-cols-2 gap-2">
                <div>
                  <div className="relative">
                    <Select
                      options={MODAL_YEAR_OPTIONS}
                      placeholder="Model"
                      onChange={e => handleSelectChange('model', e)}
                      className="dark:bg-dark-900 w-100"
                    />
                    <span className="pointer-events-none absolute top-[22px] right-3 -translate-y-1/2 text-[#050013] dark:text-gray-400">
                      <ChevronDownIcon />
                    </span>
                  </div>
                  {errors?.model && (
                    <p className="text-sm text-red-500">{errors?.model}</p>
                  )}
                </div>
                <div className="relative">
                  <div>
                    <Input
                      type="text"
                      placeholder="Plate Number"
                      className="mb-3"
                      onChange={handleChange}
                      name="plateNumber"
                      value={formData.plateNumber}
                    />
                    {errors?.plateNumber && (
                      <p className="text-sm text-red-500">
                        {errors?.plateNumber}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="mb-5 grid grid-cols-2 gap-2">
                <div>
                  <div className="relative">
                    <Select
                      options={VEHICLE_OPTIONS}
                      placeholder="Vehicle Type"
                      onChange={e => handleSelectChange('vehicleType', e)}
                      className="dark:bg-dark-900 w-100"
                    />
                    <span className="pointer-events-none absolute top-[22px] right-3 -translate-y-1/2 text-[#050013] dark:text-gray-400">
                      <ChevronDownIcon />
                    </span>
                  </div>
                  {errors?.vehicleType && (
                    <p className="text-sm text-red-500">
                      {errors?.vehicleType}
                    </p>
                  )}
                </div>
                <div>
                  <div className="relative w-full">
                    <DatePicker
                      selected={
                        formData.insuranceExpiryDate
                          ? new Date(formData.insuranceExpiryDate)
                          : null
                      }
                      className="w-full rounded-lg border border-gray-300 p-2 text-gray-800"
                      onChange={handleDriverInsurance}
                      showYearDropdown
                      showMonthDropdown
                      scrollableYearDropdown
                      dateFormat="dd/MM/yyyy"
                      placeholderText="Insurance Expiry Date"
                      yearDropdownItemNumber={100}
                      minDate={new Date()}
                    />
                    <span className="pointer-events-none absolute top-[22px] right-3 -translate-y-1/2 text-[#050013] dark:text-gray-400">
                      <ChevronDownIcon />
                    </span>
                  </div>
                  {errors?.insuranceExpiryDate && (
                    <p className="text-sm text-red-500">
                      {errors?.insuranceExpiryDate}
                    </p>
                  )}
                </div>
              </div>
            </>
          )}

          <div className="relative mb-[40px]">
            <p className="mb-3 text-sm font-medium text-[#050013] dark:text-white">
              Address Details
            </p>
            <Input
              type="text"
              placeholder="Current Address"
              onChange={handleChange}
              name="address"
              value={formData.address}
            />
            {errors?.address && (
              <p className="text-sm text-red-500">{errors?.address}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
);

// eslint-disable-next-line react/display-name
const FormStep2 = React.memo(
  ({
    driverLicenceFile,
    setDriverLicenceFile,
    insuranceFile,
    setInsuranceFile,
    errors,
  }: any) => (
    <div className="mt-5 sm:mt-8">
      <p className="pb-3 text-sm font-medium text-[#050013] dark:text-white">
        Driver Licence
        <span className="text-red-500">*</span>
      </p>
      <div className="mt-3 rounded-lg border border-gray-200 bg-white p-4 shadow-none dark:border-gray-700 dark:bg-gray-800">
        <div className="flex w-full items-center justify-center">
          <label className="flex w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 py-8">
            <div className="flex items-center justify-center pt-5 pb-6">
              <Cloud />
              <p className="text-dark-grey text-sm dark:text-gray-400">
                <span className="px-3 text-[14px]">
                  Click or drag file to this area to upload
                </span>
              </p>
            </div>
            <input
              id="dropzone-file-licence"
              type="file"
              name="driverLicenceFile"
              onChange={e => setDriverLicenceFile(e.target.files?.[0] || null)}
              className="hidden"
              accept=".png,.jpg,.jpeg"
            />
          </label>
        </div>
        {driverLicenceFile && (
          <p className="py-2 text-[12px] text-green-500">
            {driverLicenceFile.name}
          </p>
        )}
        {errors.driverLicenceFile && (
          <p className="py-2 text-[12px] text-red-500">
            {errors.driverLicenceFile}
          </p>
        )}
      </div>
      <p className="py-3 text-[13px] text-[#76787A]">
        Formats accepted are PNG & JPG
      </p>
      <p className="mt-2 text-[13px] text-[#050013]">
        If you do not have a file you can see the sample below
      </p>
      <div
        className="bg-tables mt-3 flex w-full items-center justify-between p-3"
        style={{ borderRadius: '10px' }}
      >
        <div>
          <p className="text-dark mb-1 text-sm font-semibold">
            Sample Certificate
          </p>
          <p className="text-sm text-gray-400">PNG 1.2MB</p>
        </div>
        <div className="flex items-center gap-2 font-semibold text-blue-800">
          <GrDocumentDownload className="h-[20px] w-[20px]" />
          <p className="text-sm">Download</p>
        </div>
      </div>
      <div className="flex items-center justify-end gap-2 border-b py-6">
        <button
          type="button"
          aria-label="Cancel"
          className="rounded-lg border border-gray-200 bg-white px-10 py-2.5 text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          style={{ borderRadius: '50px' }}
        >
          Cancel
        </button>
        <button
          type="button"
          aria-label="Upload"
          className="flex items-center gap-2 rounded-lg bg-gray-800 px-5 py-2.5 text-sm font-medium text-white hover:bg-gray-900 focus:ring-4 focus:ring-gray-300 focus:outline-none dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700"
          style={{ borderRadius: '50px' }}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth="1.5"
            stroke="currentColor"
            className="size-4"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
            />
          </svg>
          <span>Upload</span>
        </button>
      </div>
      <p className="pt-6 text-sm font-medium text-[#050013] dark:text-white">
        Insurance Copy
        <span className="text-red-500">*</span>
      </p>
      <div className="mt-3 rounded-lg border border-gray-200 bg-white p-4 shadow-none dark:border-gray-700 dark:bg-gray-800">
        <div className="flex w-full items-center justify-center">
          <label className="flex w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 py-5">
            <div className="flex items-center justify-center pt-5 pb-6">
              <Cloud />
              <p className="flex items-center justify-center pt-5 pb-6 text-[14px]">
                <span className="text-dark-grey px-3 dark:text-gray-400">
                  Click or drag file to this area to upload
                </span>
              </p>
            </div>
            <input
              id="dropzone-file-insurance"
              type="file"
              name="insuranceFile"
              className="hidden"
              onChange={e => setInsuranceFile(e.target.files?.[0] || null)}
              accept=".png,.jpg,.jpeg"
            />
          </label>
        </div>
        {insuranceFile && (
          <p className="py-2 text-[12px] text-green-500">
            {insuranceFile?.name}
          </p>
        )}
        {errors.insuranceFile && (
          <p className="py-2 text-[12px] text-red-500">
            {errors.insuranceFile}
          </p>
        )}
      </div>
      <p className="py-3 text-[13px] text-[#76787A]">
        Formats accepted are PNG & JPG
      </p>
      <p className="mt-2 text-[13px]">
        If you do not have a file you can see the sample below
      </p>
      <div
        className="bg-tables mt-3 flex w-full items-center justify-between p-3"
        style={{ borderRadius: '10px' }}
      >
        <div>
          <p className="text-dark mb-1 text-sm font-semibold">
            Sample Certificate
          </p>
          <p className="text-sm text-gray-400">PNG 1.2MB</p>
        </div>
        <div className="flex items-center gap-2 font-semibold text-blue-800">
          <GrDocumentDownload className="h-[20px] w-[20px]" />
          <p className="text-sm text-blue-800">Download</p>
        </div>
      </div>
    </div>
  )
);

// Main Component
export default function DriverTable() {
  const router = useRouter();

  // State management
  const [open, setOpen] = useState(false);
  const [openApproveDocument, setOpenApproveDocument] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [filterName, setFilterName] = useState('');
  const [filters, setFilters] = useState<Filters>(INITIAL_FILTERS);
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [driverType, setDriverType] = useState('company-driver');
  const [deleteProfile, setDeleteProfile] = useState(false);
  const [filteredData, setFilteredData] = useState([]);
  const [selectedId, setSelectedId] = useState<string | null>(null);

  // Custom hooks
  const {
    formData,
    driverLicenceFile,
    insuranceFile,
    errors,
    setErrors,
    handleChange,
    handleDriverDob,
    handleDriverInsurance,
    handleSelectChange,
    setDriverLicenceFile,
    setInsuranceFile,
    resetForm,
  } = useDriverForm();

  const { validate, validateFiles } = useDriverValidation();

  // Data fetching
  const { data: driverData, isLoading } = useQuery<DriverResponseType>({
    queryKey: ['driverData'],
    queryFn: getTableDriverDetails,
  });

  // Mutations
  const addDriverMutation = useMutation({
    mutationFn: addTableDriverDetails,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['driverData'] });
      toast.success('Driver Added Successfully', {
        autoClose: 5000,
        position: 'top-center',
      });
      setOpen(false);
      setCurrentStep(1);
      resetForm();
      setFilteredData([]);
    },
    onError: err => console.error(err),
  });

  const downloadDriverMutation = useMutation({
    mutationFn: downloadReport,
    onSuccess: downloadFile,
    onError: err => console.error('Download error', err),
  });

  const deleteDriverMutation = useMutation({
    mutationFn: deleteDriver,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['driverData'] });
      setDeleteProfile(false);
      toast.success('Driver Deleted Successfully', {
        autoClose: 5000,
        position: 'top-center',
      });
    },
    onError: err => console.error(err),
  });

  const disableDriverMutation = useMutation({
    mutationFn: disableDriverQuery,
    onSuccess: (data: { driverStatus: string }) => {
      queryClient.invalidateQueries({ queryKey: ['driverData'] });
      toast.success(`Driver ${data?.driverStatus} Successfully`, {
        autoClose: 5000,
        position: 'top-center',
      });
    },
    onError: err => console.error(err),
  });

  // Memoized callbacks
  const handleSelectOrder = useCallback((id: number) => {
    setSelectedIds(prev =>
      prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]
    );
  }, []);

  const handleSelectAll = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.checked) {
        const allIds = sortedData.map((driver: any) => driver.id);
        setSelectedIds(allIds);
      } else {
        setSelectedIds([]);
      }
    },
    []
  );

  const handleDeleteDriver = useCallback(
    (driverId: string) => {
      if (driverId) {
        deleteDriverMutation.mutate(driverId);
      }
    },
    [deleteDriverMutation]
  );

  const handleDisableDriver = useCallback(
    (driverId: string, status: string) => {
      const data = { driverId, status };
      if (data) {
        disableDriverMutation.mutate(data);
      }
    },
    [disableDriverMutation]
  );

  const requestSort = useCallback(
    (key: string) => {
      let direction: 'asc' | 'desc' = 'asc';
      if (
        sortConfig &&
        sortConfig.key === key &&
        sortConfig.direction === 'asc'
      ) {
        direction = 'desc';
      }
      setSortConfig({ key, direction });
    },
    [sortConfig]
  );

  const getSortIcon = useCallback(
    (key: string) => {
      if (!sortConfig || sortConfig.key !== key) return '⇅';
      return sortConfig.direction === 'asc' ? '↑' : '↓';
    },
    [sortConfig]
  );

  const handleSubmit = useCallback(() => {
    setIsSubmitting(true);
    const { errors: validationErrors, isValid } = validate(formData);

    if (!isValid) {
      setErrors(validationErrors);
      setIsSubmitting(false);
      return;
    }

    setCurrentStep(2);
    setIsSubmitting(false);
  }, [formData, validate, setErrors]);

  const handleFormSubmit = useCallback(() => {
    const { errors: fileErrors, isValid } = validateFiles(
      driverLicenceFile,
      insuranceFile
    );

    if (!isValid) {
      setErrors(fileErrors);
      return;
    }

    setIsSubmitting(true);
    const payload = {
      ...formData,
      driverLicenceFile,
      insuranceFile,
      driverType,
    };

    addDriverMutation.mutate(payload, {
      onSettled: () => setIsSubmitting(false),
    });
  }, [
    formData,
    driverLicenceFile,
    insuranceFile,
    driverType,
    validateFiles,
    addDriverMutation,
  ]);

  // Filter and sort logic
  const filtersDataFuc = useCallback(() => {
    if (!driverData || !Array.isArray(driverData.data)) {
      setFilteredData([]);
      return;
    }

    const data = driverData.data.filter((driver: any) => {
      const driverName = driver.fullName?.toLowerCase() || '';
      const matchesSearch =
        !searchTerm.trim() || driverName.includes(searchTerm.toLowerCase());
      const matchesDriverSearch =
        !filterName.trim() || driverName.includes(filterName.toLowerCase());
      const matchesName =
        !filters.name || driverName.includes(filters.name.toLowerCase());
      const matchesShifts =
        !filters.shift ||
        driver.shift?.toLowerCase() === filters.shift.toLowerCase();
      const matchesRatings =
        !filters.ratings || String(driver.rating) === String(filters.ratings);
      const matchesRegion =
        !filters.region ||
        driver.region?.toLowerCase() === filters.region.toLowerCase();
      const matchesRides =
        !filters.rides || String(driver.rides) === String(filters.rides);

      return (
        matchesSearch &&
        matchesName &&
        matchesShifts &&
        matchesRatings &&
        matchesRegion &&
        matchesRides &&
        matchesDriverSearch
      );
    });

    setFilteredData(data);
  }, [driverData, searchTerm, filterName, filters]);

  const sortedData = useMemo(() => {
    if (!sortConfig) return filteredData;

    return [...filteredData].sort((a, b) => {
      if (['id', 'rating', 'rides', 'earnings'].includes(sortConfig.key)) {
        const aValue = parseFloat(a[sortConfig.key]) || 0;
        const bValue = parseFloat(b[sortConfig.key]) || 0;
        return sortConfig.direction === 'asc'
          ? aValue - bValue
          : bValue - aValue;
      }

      if (sortConfig.key === 'dateTime') {
        const aDate = new Date(a[sortConfig.key]);
        const bDate = new Date(b[sortConfig.key]);
        return sortConfig.direction === 'asc'
          ? aDate.getTime() - bDate.getTime()
          : bDate.getTime() - aDate.getTime();
      }

      const aValue = a[sortConfig.key]?.toString().toLowerCase() || '';
      const bValue = b[sortConfig.key]?.toString().toLowerCase() || '';

      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });
  }, [filteredData, sortConfig]);

  // Effects
  useEffect(() => {
    filtersDataFuc();
  }, [filtersDataFuc]);

  const resetFilters = useCallback(() => {
    setFilters(INITIAL_FILTERS);
    setFilterName('');
  }, []);

  return (
    <>
      <div className="tabled">
        <div className="flex justify-end">
          <button
            onClick={() => setOpen(true)}
            type="button"
            className="bg-cstm-blue-700 me-2 mb-4 flex items-center gap-2 rounded-full px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
          >
            <FaPlus />
            Add New Driver
          </button>
        </div>

        <div className="overflow-visible rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
          <div className="header-bar bg-table-head flex items-center justify-between rounded-t-[12px] px-3 py-1">
            <form className="max-w-md flex-1">
              <label className="sr-only mb-2 text-sm font-medium text-gray-900 dark:text-white">
                Search
              </label>
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3">
                  <IoIosSearch className="h-[20px] w-[20px] text-[#050013]" />
                </div>
                <input
                  type="search"
                  id="default-search"
                  className="block w-3/4 rounded-full border border-0 border-gray-300 border-transparent bg-white p-2 ps-10 text-sm text-gray-900 shadow-[0_10px_40px_0_#0000000D] focus:border-blue-500 focus:ring-blue-500 dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                  placeholder="Search here"
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                />
              </div>
            </form>

            <div className="flex items-center gap-2">
              <FilterComponent
                filteredData={driverData}
                setFilters={setFilters}
                filtersDataFuc={filtersDataFuc}
                setFilterName={setFilterName}
              />
              <button
                type="button"
                aria-label="reset filters"
                onClick={resetFilters}
                className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-1 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
              >
                <PiClockCounterClockwiseLight size={24} />
              </button>
              <button
                aria-label="download"
                onClick={() => downloadDriverMutation.mutate(selectedIds)}
                type="button"
                disabled={selectedIds.length === 0}
                className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
              >
                <DownloadIcon />
              </button>
            </div>
          </div>

          <div className="custom-scrollbar max-w-full overflow-x-auto">
            <div className="max-w-[992px] min-w-[-webkit-fill-available]">
              <Table>
                <TableHeaderComponent
                  sortConfig={sortConfig}
                  requestSort={requestSort}
                  getSortIcon={getSortIcon}
                  selectedIds={selectedIds}
                  sortedData={sortedData}
                  handleSelectAll={handleSelectAll}
                />

                <TableBody className="divide-y bg-white dark:divide-white/[0.05]">
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={13} className="py-8 text-center">
                        Loading drivers...
                      </TableCell>
                    </TableRow>
                  ) : !driverData?.data?.length ? (
                    <TableRow>
                      <TableCell colSpan={13} className="py-8 text-center">
                        No drivers found
                      </TableCell>
                    </TableRow>
                  ) : (
                    sortedData.map((order: any) => (
                      <TableRow
                        key={order.id}
                        className="cursor-pointer dark:hover:bg-gray-800"
                      >
                        <TableCell className="px-4 py-2">
                          <label className="checkbox">
                            <input
                              type="checkbox"
                              checked={selectedIds.includes(order.id)}
                              onChange={() => handleSelectOrder(order.id)}
                              className="form-checkbox text-blue-500"
                            />
                            <span></span>
                          </label>
                        </TableCell>
                        <TableCell className="px-4 py-3">
                          <p className="text-xs text-[#050013]">#{order?.id}</p>
                        </TableCell>
                        <TableCell className="px-4 py-3">
                          <p
                            className="text-[12px] text-[#050013] capitalize cursor-pointer"
                            onClick={() =>
                              router.push(`/admin/drivers/${order?.id}`)
                            }
                          >
                            {order?.fullName}
                          </p>
                        </TableCell>
                        <TableCell className="px-4 py-3">
                          <p className="text-[12px] text-[#050013]">
                            {order?.contactNo}
                          </p>
                        </TableCell>
                        <TableCell className="px-4 py-3">
                          <p
                            className={`text-[13px] capitalize ${getDriverStatusClass(
                              order?.driverStatus
                            )}`}
                          >
                            {order?.driverStatus}
                          </p>
                        </TableCell>
                        <TableCell className="px-4 py-3">
                          <p className="text-[12px] text-[#050013] capitalize">
                            {order?.shift}
                          </p>
                        </TableCell>
                        <TableCell className="px-4 py-3">
                          <p className="flex items-center gap-1 text-[12px] text-[#050013]">
                            {order?.rating} <StarIcon />
                          </p>
                        </TableCell>
                        <TableCell className="px-4 py-3">
                          <p className="text-[12px] text-[#050013] capitalize">
                            {order?.region}
                          </p>
                        </TableCell>
                        <TableCell className="px-4 py-3">
                          <p className="text-[12px] text-[#050013] uppercase">
                            {order?.plateNumber}
                          </p>
                        </TableCell>
                        <TableCell className="px-4 py-3">
                          <p className="text-[12px] text-[#050013]">
                            {order?.rides}
                          </p>
                        </TableCell>
                        <TableCell className="px-4 py-3">
                          <p className="text-[12px] text-[#050013]">
                            €{order?.earnings}
                          </p>
                        </TableCell>
                        <TableCell className="px-4 py-3">
                          <p
                            className={`${
                              order?.registrationstatus === 'Verified'
                                ? 'text-[#00CA77]'
                                : 'text-[#FF7C00]'
                            } flex items-center gap-1 text-[11px]`}
                          >
                            <span
                              className={`block h-[6px] w-[6px] rounded-full ${
                                order?.registrationstatus === 'Verified'
                                  ? 'bg-[#00CA77]'
                                  : 'bg-[#FF7C00]'
                              }`}
                            ></span>
                            {order?.registrationstatus ? 'Verified' : 'Pending'}
                          </p>
                        </TableCell>
                        <TableCell className="px-4 py-3">
                          <ActionMenu
                            order={order}
                            router={router}
                            handleDisableDriver={handleDisableDriver}
                            setOpenApproveDocument={setOpenApproveDocument}
                            downLoadDriverMutation={downloadDriverMutation}
                            setSelectedId={setSelectedId}
                            setDeleteProfile={setDeleteProfile}
                          />
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Dialog for Add Driver */}
          <Dialog
            open={open}
            onClose={setOpen}
            className="relative z-100000"
            style={{ borderTopLeftRadius: '50px' }}
          >
            <DialogBackdrop
              transition
              className="fixed inset-0 bg-[#2a2a2a] opacity-60 transition-opacity duration-500 ease-in-out data-closed:opacity-0"
            />
            <div className="fixed inset-0 overflow-hidden">
              <div className="absolute inset-0 overflow-hidden">
                <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
                  <DialogPanel
                    transition
                    style={{ borderTopLeftRadius: '50px !important' }}
                    className="pointer-events-auto relative w-full max-w-4xl transform rounded-tl-[50px] transition duration-500 ease-in-out data-closed:translate-x-full sm:w-[500px] sm:duration-700 md:w-[700px] lg:w-[500px] xl:w-[600px]"
                  >
                    <div className="flex h-full flex-col overflow-y-scroll rounded-2xl bg-white shadow-xl">
                      <div className="bg-[#F6F8FB] py-8 sm:px-6">
                        <DialogTitle className="align-center flex items-center justify-between py-5 text-[20px] font-semibold text-gray-900">
                          <div>Driver Details</div>
                          <IoMdClose
                            className="text-[#76787A] hover:text-[#3324E3] cursor-pointer"
                            size={20}
                            onClick={() => setOpen(false)}
                          />
                        </DialogTitle>
                        <ul className="flex w-full items-center">
                          {STEPS.map((step, index) => {
                            const stepIndex = index + 1;
                            const isActive = currentStep === stepIndex;
                            const isSecondStep = currentStep === 2;
                            const IconComponent = isSecondStep
                              ? step.active
                              : step.icon;

                            return (
                              <li
                                key={stepIndex}
                                className="flex w-full items-center"
                              >
                                <div className="relative flex items-center">
                                  <div
                                    className={`flex size-12 items-center justify-center rounded-full border ${
                                      isSecondStep && stepIndex === 1
                                        ? 'border-green'
                                        : 'border-gray-300'
                                    }`}
                                  >
                                    <IconComponent
                                      size={24}
                                      className="text-gray-600"
                                    />
                                  </div>
                                  {stepIndex === 1 && isSecondStep && (
                                    <div className="absolute bottom-[20px] left-[25px]">
                                      <UserTick />
                                    </div>
                                  )}
                                </div>

                                <div className="ml-2 flex flex-col">
                                  <span
                                    className={`text-sm ${
                                      isActive
                                        ? isSecondStep && stepIndex === 2
                                          ? 'text-blue-6 font-medium'
                                          : 'font-medium text-[#050013]'
                                        : 'text-gray-900'
                                    }`}
                                  >
                                    {step.label}
                                  </span>
                                  <span className="text-xs text-gray-500">
                                    {step.subtitle}
                                  </span>
                                </div>

                                {stepIndex < STEPS.length && (
                                  <hr
                                    className={`mx-4 h-0 flex-1 border-t transition-colors duration-300 ${
                                      isSecondStep && stepIndex === 1
                                        ? 'border-blue-600'
                                        : 'border-gray-300'
                                    }`}
                                  />
                                )}
                              </li>
                            );
                          })}
                        </ul>
                      </div>
                      <div className="custom-scrollbar relative mb-6 flex-1 overflow-y-scroll px-4 sm:px-6">
                        <div>
                          {currentStep === 1 && (
                            <FormStep1
                              driverType={driverType}
                              setDriverType={setDriverType}
                              formData={formData}
                              errors={errors}
                              handleChange={handleChange}
                              handleSelectChange={handleSelectChange}
                              handleDriverDob={handleDriverDob}
                              handleDriverInsurance={handleDriverInsurance}
                            />
                          )}

                          {currentStep === 2 && (
                            <FormStep2
                              driverLicenceFile={driverLicenceFile}
                              setDriverLicenceFile={setDriverLicenceFile}
                              insuranceFile={insuranceFile}
                              setInsuranceFile={setInsuranceFile}
                              errors={errors}
                            />
                          )}
                        </div>
                      </div>

                      <div className="border-grey-700 flex w-full justify-end border-t py-6">
                        {currentStep === 1 ? (
                          <button
                            onClick={handleSubmit}
                            type="button"
                            disabled={isSubmitting}
                            className={`bg-cstm-blue-700 me-2 mb-2 flex items-center gap-2 rounded-full bg-blue-700 px-6 py-3 text-center text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 ${
                              isSubmitting
                                ? 'cursor-not-allowed opacity-75'
                                : ''
                            }`}
                          >
                            {isSubmitting ? (
                              <>
                                <LoadingSpinner />
                                Processing...
                              </>
                            ) : (
                              'Continue'
                            )}
                          </button>
                        ) : null}

                        {currentStep === 2 ? (
                          <button
                            type="button"
                            onClick={handleFormSubmit}
                            disabled={
                              isSubmitting || addDriverMutation.isPending
                            }
                            className={`bg-cstm-blue-700 me-2 mb-2 flex items-center gap-2 rounded-full bg-blue-700 px-5 py-3 text-center text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 ${
                              isSubmitting || addDriverMutation.isPending
                                ? 'cursor-not-allowed opacity-75'
                                : ''
                            }`}
                          >
                            {isSubmitting || addDriverMutation.isPending ? (
                              <>
                                <LoadingSpinner />
                                Adding Driver...
                              </>
                            ) : (
                              'Add New Driver'
                            )}
                          </button>
                        ) : null}
                      </div>
                    </div>
                  </DialogPanel>
                </div>
              </div>
            </div>
          </Dialog>
        </div>
      </div>

      {selectedId && (
        <DeleteModal
          id={selectedId}
          isOpen={deleteProfile}
          setIsOpen={setDeleteProfile}
          handleDeleteDriver={id => {
            handleDeleteDriver(id);
            setSelectedId(null);
          }}
          title="Delete Document"
          message="Are you sure you want to delete this document? This cannot be undone."
        />
      )}

      {openApproveDocument && (
        <ApproveDocument
          open={openApproveDocument}
          setOpen={setOpenApproveDocument}
        />
      )}
    </>
  );
}
